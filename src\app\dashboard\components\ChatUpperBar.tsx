'use client';

import { useState } from 'react';
import ChatStatisticsModal from './ChatStatisticsModal';
import FileSelectionModal from './FileSelectionModal';

interface ChatUpperBarProps {
  chatName: string;
  currentModel: string;
  sessionTime: string;
  isLoading: boolean;
  onDownloadClick?: () => void;
  onFileSelectionClick?: () => void;
  aiMetadata?: {
    usedCoT: boolean;
    confidence: number;
    processingTime: number;
  } | null;
  chatId?: string;
}

const ChatUpperBar = ({ chatName, currentModel, sessionTime, isLoading, onDownloadClick, onFileSelectionClick, aiMetadata, chatId }: ChatUpperBarProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isStatsModalOpen, setIsStatsModalOpen] = useState(false);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleStats = () => {
    setIsStatsModalOpen(true);
  };

  const handleDownload = () => {
    onDownloadClick?.();
  };

  const handleFileSelection = () => {
    onFileSelectionClick?.();
  };

  return (
    <div className="h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      <div className="h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10">
        {/* Left Section - Model and Session Time */}
        <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
          <div className="flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0"></div>
            <span className="text-xs sm:text-sm font-medium text-blue-100 truncate">{currentModel}</span>
            {aiMetadata?.usedCoT && (
              <div className="px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block">
                CoT
              </div>
            )}
          </div>

          <div className="w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block"></div>

          <div className="flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex">
            <svg className="w-3 h-3 sm:w-4 sm:h-4 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-xs sm:text-sm text-blue-200 font-mono">{sessionTime}</span>
          </div>


        </div>

        {/* Center Section - Chat Name */}
        <div className="absolute left-1/2 transform -translate-x-1/2 hidden sm:block">
          {isLoading ? (
            <div className="h-6 sm:h-8 bg-blue-700/30 rounded-lg sm:rounded-xl w-32 sm:w-48 backdrop-blur-sm"></div>
          ) : (
            <div className="flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-cyan-400 rounded-full shadow-lg shadow-cyan-400/50 flex-shrink-0"></div>
              <h1 className="text-sm sm:text-lg font-semibold text-white truncate">
                {chatName}
              </h1>
            </div>
          )}
        </div>

        {/* Right Section - Action Buttons */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          <button
            onClick={toggleFullscreen}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 hidden sm:block"
            title="Tela cheia"
          >
            {isFullscreen ? (
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
            )}
          </button>

          <button
            onClick={handleFileSelection}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105"
            title="Selecionar Arquivos"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 000-2.828L15.172 7zM9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          <button
            onClick={handleStats}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105"
            title="Estatísticas"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </button>

          <button
            onClick={handleDownload}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30"
            title="Download"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Modal de Estatísticas */}
      {chatId && (
        <ChatStatisticsModal
          isOpen={isStatsModalOpen}
          onClose={() => setIsStatsModalOpen(false)}
          chatId={chatId}
          chatName={chatName}
        />
      )}
    </div>
  );
};

export default ChatUpperBar;
