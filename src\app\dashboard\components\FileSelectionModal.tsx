'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { attachmentService } from '@/lib/attachmentService';
import { AttachmentMetadata } from '@/lib/types/chat';

interface FileSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  chatId: string;
  chatName: string;
  onFilesSelected: (files: AttachmentMetadata[]) => void;
  existingAttachments?: AttachmentMetadata[];
}

const FileSelectionModal = ({
  isOpen,
  onClose,
  chatId,
  chatName,
  onFilesSelected,
  existingAttachments = []
}: FileSelectionModalProps) => {
  const { user } = useAuth();
  const [selectedFiles, setSelectedFiles] = useState<AttachmentMetadata[]>([]);
  const [selectedExistingFiles, setSelectedExistingFiles] = useState<Set<string>>(new Set());
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset selected files when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedFiles([]);
      setSelectedExistingFiles(new Set());
    }
  }, [isOpen]);

  // Close modal with ESC key
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !user) return;

    await uploadFiles(Array.from(files));
  };

  const uploadFiles = async (files: File[]) => {
    setIsUploading(true);
    try {
      const result = await attachmentService.uploadMultipleFiles(
        user.uid,
        chatId,
        files,
        chatName
      );

      if (result.success && result.attachments.length > 0) {
        setSelectedFiles(prev => [...prev, ...result.attachments]);
      }

      if (result.errors.length > 0) {
        console.error('Upload errors:', result.errors);
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      // TODO: Show error toast
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (!user) return;

    const files = Array.from(e.dataTransfer.files);
    const supportedFiles = files.filter(file => 
      attachmentService.validateFile(file).valid
    );

    if (supportedFiles.length > 0) {
      await uploadFiles(supportedFiles);
    }
  };

  const removeFile = (fileId: string) => {
    setSelectedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const toggleExistingFile = (fileId: string) => {
    setSelectedExistingFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileId)) {
        newSet.delete(fileId);
      } else {
        newSet.add(fileId);
      }
      return newSet;
    });
  };

  const handleConfirm = () => {
    // Combinar arquivos recém-selecionados com arquivos existentes selecionados
    const existingSelected = existingAttachments.filter(file =>
      selectedExistingFiles.has(file.id)
    );
    const allSelectedFiles = [...existingSelected, ...selectedFiles];
    onFilesSelected(allSelectedFiles);
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      {/* Modal */}
      <div className="relative bg-blue-900/95 border border-blue-700/30 rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-blue-700/30">
          <div>
            <h2 className="text-xl font-semibold text-white">
              Selecionar Arquivos
            </h2>
            <p className="text-blue-300/70 text-sm mt-1">
              Selecione arquivos existentes ou adicione novos para usar no próximo prompt
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg bg-blue-800/40 hover:bg-blue-700/40 text-blue-300 hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(80vh-140px)]">
          {/* Existing Files */}
          {existingAttachments.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-white mb-4">
                Arquivos Disponíveis ({existingAttachments.length})
              </h3>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {existingAttachments.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-4 rounded-lg border transition-all cursor-pointer ${
                      selectedExistingFiles.has(file.id)
                        ? 'bg-blue-600/30 border-blue-500/60'
                        : 'bg-blue-800/30 border-blue-600/30 hover:bg-blue-700/40'
                    }`}
                    onClick={() => toggleExistingFile(file.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <input
                          type="checkbox"
                          checked={selectedExistingFiles.has(file.id)}
                          onChange={() => toggleExistingFile(file.id)}
                          className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 rounded focus:ring-blue-500"
                        />
                      </div>
                      <div className="p-2 bg-blue-700/40 rounded-lg">
                        {file.type === 'image' ? (
                          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <p className="text-white font-medium truncate max-w-xs">
                          {file.filename}
                        </p>
                        <p className="text-blue-300/70 text-sm">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Divider */}
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-blue-600/30"></div>
              <span className="px-4 text-sm text-blue-300/70">ou</span>
              <div className="flex-1 h-px bg-blue-600/30"></div>
            </div>
          )}

          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
              dragActive
                ? 'border-blue-400 bg-blue-500/10'
                : 'border-blue-600/40 hover:border-blue-500/60 hover:bg-blue-800/20'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-blue-800/30 rounded-full">
                <svg className="w-8 h-8 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <div>
                <p className="text-white font-medium mb-2">
                  Arraste arquivos aqui ou clique para selecionar
                </p>
                <p className="text-blue-300/70 text-sm">
                  Suportamos imagens (PNG, JPEG, WebP) e PDFs
                </p>
              </div>
              <button
                onClick={handleFileSelect}
                disabled={isUploading}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors font-medium disabled:cursor-not-allowed"
              >
                {isUploading ? 'Enviando...' : 'Selecionar Arquivos'}
              </button>
            </div>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-white mb-4">
                Arquivos Selecionados ({selectedFiles.length})
              </h3>
              <div className="space-y-3">
                {selectedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-4 bg-blue-800/30 rounded-lg border border-blue-600/30"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-700/40 rounded-lg">
                        {file.type === 'image' ? (
                          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <p className="text-white font-medium truncate max-w-xs">
                          {file.filename}
                        </p>
                        <p className="text-blue-300/70 text-sm">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(file.id)}
                      className="p-2 rounded-lg bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300 transition-colors"
                      title="Remover arquivo"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-blue-700/30 bg-blue-900/50">
          <div className="text-sm text-blue-300/70">
            {(() => {
              const totalSelected = selectedExistingFiles.size + selectedFiles.length;
              if (totalSelected === 0) return 'Nenhum arquivo selecionado';

              const parts = [];
              if (selectedExistingFiles.size > 0) {
                parts.push(`${selectedExistingFiles.size} existente(s)`);
              }
              if (selectedFiles.length > 0) {
                parts.push(`${selectedFiles.length} novo(s)`);
              }

              return `${totalSelected} arquivo(s): ${parts.join(', ')}`;
            })()}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-800/40 hover:bg-blue-700/40 text-blue-300 hover:text-white rounded-lg transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleConfirm}
              disabled={selectedFiles.length === 0 && selectedExistingFiles.size === 0}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors font-medium disabled:cursor-not-allowed"
            >
              Confirmar Seleção
            </button>
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.png,.jpg,.jpeg,.webp"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default FileSelectionModal;
